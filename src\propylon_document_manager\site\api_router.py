from django.conf import settings
from rest_framework.routers import <PERSON>fa<PERSON><PERSON><PERSON><PERSON>, SimpleRouter
from propylon_document_manager.file_versions.api.views import UploadFileVersionViewSet

from propylon_document_manager.file_versions.api.views import FileVersionViewSet

if settings.DEBUG:
    router = DefaultRouter()
else:
    router = SimpleRouter()

router.register("file_versions", FileVersionViewSet)
router.register("upload", UploadFileVersionViewSet, basename="file-upload")



app_name = "api"
urlpatterns = router.urls
