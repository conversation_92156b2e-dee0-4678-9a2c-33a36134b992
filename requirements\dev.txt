#
# This file is autogenerated by pip-compile with Python 3.11
# by the following command:
#
#    pip-compile requirements/local.in
#
argon2-cffi==23.1.0
    # via -r requirements/base.in
argon2-cffi-bindings==21.2.0
    # via argon2-cffi
asgiref==3.7.2
    # via
    #   django
    #   django-cors-headers
astroid==3.0.2
    # via pylint
asttokens==2.4.1
    # via stack-data
black==23.12.1
    # via -r requirements/local.in
certifi==2023.11.17
    # via requests
cffi==1.16.0
    # via
    #   argon2-cffi-bindings
    #   cryptography
cfgv==3.4.0
    # via pre-commit
charset-normalizer==3.3.2
    # via requests
click==8.1.7
    # via black
coverage==7.4.0
    # via
    #   -r requirements/local.in
    #   django-coverage-plugin
cryptography==41.0.7
    # via pyjwt
decorator==5.1.1
    # via
    #   ipdb
    #   ipython
defusedxml==0.7.1
    # via python3-openid
dill==0.3.7
    # via pylint
distlib==0.3.8
    # via virtualenv
django==5.0.1
    # via
    #   -r requirements/base.in
    #   django-allauth
    #   django-cors-headers
    #   django-debug-toolbar
    #   django-extensions
    #   django-model-utils
    #   django-stubs
    #   django-stubs-ext
    #   djangorestframework
django-allauth==0.60.0
    # via -r requirements/base.in
django-cors-headers==4.3.1
    # via -r requirements/base.in
django-coverage-plugin==3.1.0
    # via -r requirements/local.in
django-debug-toolbar==4.2.0
    # via -r requirements/local.in
django-environ==0.11.2
    # via -r requirements/base.in
django-extensions==3.2.3
    # via -r requirements/local.in
django-model-utils==4.3.1
    # via -r requirements/base.in
django-stubs==4.2.7
    # via
    #   -r requirements/local.in
    #   djangorestframework-stubs
django-stubs-ext==4.2.7
    # via django-stubs
djangorestframework==3.14.0
    # via -r requirements/base.in
djangorestframework-stubs==3.14.5
    # via -r requirements/local.in
executing==2.0.1
    # via stack-data
factory-boy==3.3.0
    # via -r requirements/local.in
faker==22.2.0
    # via factory-boy
filelock==3.13.1
    # via virtualenv
flake8==7.0.0
    # via
    #   -r requirements/local.in
    #   flake8-isort
flake8-isort==6.1.1
    # via -r requirements/local.in
identify==2.5.33
    # via pre-commit
idna==3.6
    # via requests
iniconfig==2.0.0
    # via pytest
ipdb==0.13.13
    # via -r requirements/local.in
ipython==8.20.0
    # via ipdb
isort==5.13.2
    # via
    #   flake8-isort
    #   pylint
jedi==0.19.1
    # via ipython
matplotlib-inline==0.1.6
    # via ipython
mccabe==0.7.0
    # via
    #   flake8
    #   pylint
mypy==1.8.0
    # via -r requirements/local.in
mypy-extensions==1.0.0
    # via
    #   black
    #   mypy
nodeenv==1.8.0
    # via pre-commit
oauthlib==3.2.2
    # via requests-oauthlib
packaging==23.2
    # via
    #   black
    #   pytest
    #   pytest-sugar
parso==0.8.3
    # via jedi
pathspec==0.12.1
    # via black
pexpect==4.9.0
    # via ipython
pillow==10.2.0
    # via -r requirements/base.in
platformdirs==4.1.0
    # via
    #   black
    #   pylint
    #   virtualenv
pluggy==1.3.0
    # via pytest
pre-commit==3.6.0
    # via -r requirements/local.in
prompt-toolkit==3.0.43
    # via ipython
psycopg2-binary==2.9.9
    # via -r requirements/local.in
ptyprocess==0.7.0
    # via pexpect
pure-eval==0.2.2
    # via stack-data
pycodestyle==2.11.1
    # via flake8
pycparser==2.21
    # via cffi
pyflakes==3.2.0
    # via flake8
pygments==2.17.2
    # via ipython
pyjwt[crypto]==2.8.0
    # via django-allauth
pylint==3.0.3
    # via
    #   pylint-django
    #   pylint-plugin-utils
pylint-django==2.5.5
    # via -r requirements/local.in
pylint-plugin-utils==0.8.2
    # via pylint-django
pytest==7.4.4
    # via
    #   -r requirements/local.in
    #   pytest-django
    #   pytest-sugar
pytest-django==4.7.0
    # via -r requirements/local.in
pytest-sugar==0.9.7
    # via -r requirements/local.in
python-dateutil==2.8.2
    # via faker
python-slugify==8.0.1
    # via -r requirements/base.in
python3-openid==3.2.0
    # via django-allauth
pytz==2023.3.post1
    # via djangorestframework
pyyaml==6.0.1
    # via pre-commit
requests==2.31.0
    # via
    #   django-allauth
    #   djangorestframework-stubs
    #   requests-oauthlib
requests-oauthlib==1.3.1
    # via django-allauth
six==1.16.0
    # via
    #   asttokens
    #   python-dateutil
sqlparse==0.4.4
    # via
    #   django
    #   django-debug-toolbar
stack-data==0.6.3
    # via ipython
termcolor==2.4.0
    # via pytest-sugar
text-unidecode==1.3
    # via python-slugify
tomlkit==0.12.3
    # via pylint
traitlets==5.14.1
    # via
    #   ipython
    #   matplotlib-inline
types-pytz==2023.3.1.1
    # via django-stubs
types-pyyaml==*********
    # via
    #   django-stubs
    #   djangorestframework-stubs
types-requests==2.31.0.20240106
    # via djangorestframework-stubs
typing-extensions==4.9.0
    # via
    #   django-stubs
    #   django-stubs-ext
    #   djangorestframework-stubs
    #   mypy
urllib3==2.1.0
    # via
    #   requests
    #   types-requests
virtualenv==20.25.0
    # via pre-commit
wcwidth==0.2.13
    # via prompt-toolkit
whitenoise==6.6.0
    # via -r requirements/base.in

# The following packages are considered to be unsafe in a requirements file:
# setuptools
