// import React, { useState, useEffect } from "react";

// import "./FileVersions.css";

// function FileVersionsList(props) {
//   const file_versions = props.file_versions;
//   return file_versions.map((file_version) => (
//     <div className="file-version" key={file_version.id}>
//       <h2>File Name: {file_version.file_name}</h2>
//       <p>
//         ID: {file_version.id} Version: {file_version.version_number}
//       </p>
//     </div>
//   ));
// }
// function FileVersions() {
//   const [data, setData] = useState([]);
//   console.log(data);

//   useEffect(() => {
//     // fetch data
//     const dataFetch = async () => {
//       const data = await (
//         await fetch("http://localhost:8000/api/file_versions")
//       ).json();

//       // set state when the data received
//       setData(data);
//     };

//     dataFetch();
//   }, []);
//   return (
//     <div>
//       <h1>Found {data.length} File Versions</h1>
//       <div>
//         <FileVersionsList file_versions={data} />h
//       </div>
//     </div>
//   );
// }

// export default FileVersions;

import React, { useState, useEffect } from "react";
import "./FileVersions.css";

function FileVersionsList(props) {
  const file_versions = props.file_versions;
  return file_versions.map((file_version) => (
    <div className="file-version" key={file_version.id}>
      <h2>File Name: {file_version.file_name}</h2>
      <p>
        ID: {file_version.id} | Version: {file_version.version_number}
      </p>
    </div>
  ));
}

function FileVersions() {
  const [data, setData] = useState([]);
  const [token, setToken] = useState(null);

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  const [uploadFile, setUploadFile] = useState(null);
  const [uploadPath, setUploadPath] = useState("");

  // 🔐 Login function
  const handleLogin = async () => {
    const res = await fetch("http://localhost:8000/auth-token/", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ username: email, password }),
    });
    const result = await res.json();
    if (result.token) {
      setToken(result.token);
    } else {
      alert("Login failed");
    }
  };

  // 📥 Fetch file versions
  const fetchVersions = async () => {
    const res = await fetch("http://localhost:8000/api/file_versions/", {
      headers: {
        Authorization: `Token ${token}`,
      },
    });
    const result = await res.json();
    setData(result);
  };

  // 📤 Upload a file
  const handleUpload = async () => {
    if (!uploadFile || !uploadPath) return alert("File and path are required");

    const formData = new FormData();
    formData.append("file", uploadFile);
    formData.append("path", uploadPath);

    const res = await fetch("http://localhost:8000/api/file_versions/upload/", {
      method: "POST",
      headers: {
        Authorization: `Token ${token}`,
      },
      body: formData,
    });

    if (res.status === 201) {
      alert("Upload successful");
      fetchVersions(); // refresh list
    } else {
      const error = await res.json();
      alert("Upload failed: " + JSON.stringify(error));
    }
  };

  // 🔃 Auto-fetch after login
  useEffect(() => {
    if (token) fetchVersions();
  }, [token]);

  return (
    <div>
      <h1>Found {data.length} File Versions</h1>

      {/* 🔐 Login */}
      {!token && (
        <div className="file-version">
          <h3>Login</h3>
          <input placeholder="Email" value={email} onChange={e => setEmail(e.target.value)} />
          <input placeholder="Password" type="password" value={password} onChange={e => setPassword(e.target.value)} />
          <button onClick={handleLogin}>Login</button>
        </div>
      )}

      {/* 📤 Upload */}
      {token && (
        <div className="file-version">
          <h3>Upload New File Version</h3>
          <input
            type="text"
            placeholder="/documents/reviews/review.pdf"
            value={uploadPath}
            onChange={e => setUploadPath(e.target.value)}
          />
          <input type="file" onChange={e => setUploadFile(e.target.files[0])} />
          <button onClick={handleUpload}>Upload</button>
        </div>
      )}

      {/* 📋 File list */}
      <div>
        <FileVersionsList file_versions={data} />
      </div>
    </div>
  );
}

export default FileVersions;
