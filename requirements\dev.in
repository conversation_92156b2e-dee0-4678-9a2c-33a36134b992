-r base.in

# Werkzeug[watchdog]==2.3.5 # https://github.com/pallets/werkzeug
ipdb  # https://github.com/gotcha/ipdb
psycopg2-binary  # https://github.com/psycopg/psycopg2

# Testing
# ------------------------------------------------------------------------------
mypy  # https://github.com/python/mypy
django-stubs  # https://github.com/typeddjango/django-stubs
pytest  # https://github.com/pytest-dev/pytest
pytest-sugar  # https://github.com/Frozenball/pytest-sugar
djangorestframework-stubs  # https://github.com/typeddjango/djangorestframework-stubs

# Code quality
# ------------------------------------------------------------------------------
flake8 # https://github.com/PyCQA/flake8
flake8-isort  # https://github.com/gforcada/flake8-isort
coverage  # https://github.com/nedbat/coveragepy
black  # https://github.com/psf/black
pylint-django  # https://github.com/PyCQA/pylint-django
pre-commit  # https://github.com/pre-commit/pre-commit

# Django
# ------------------------------------------------------------------------------
factory-boy  # https://github.com/FactoryBoy/factory_boy

django-debug-toolbar  # https://github.com/jazzband/django-debug-toolbar
django-extensions  # https://github.com/django-extensions/django-extensions
django-coverage-plugin  # https://github.com/nedbat/django_coverage_plugin
pytest-django  # https://github.com/pytest-dev/pytest-django
