from django.shortcuts import render

from django.http import Http404, FileResponse
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from urllib.parse import unquote
from rest_framework.mixins import RetrieveModelMixin, ListModelMixin
from rest_framework import viewsets, permissions
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON>ars<PERSON>, FormParser
from ..models import FileVersion
from .serializers import FileVersionSerializer, UploadFileVersionSerializer


class FileVersionViewSet(RetrieveModelMixin, ListModelMixin, viewsets.GenericViewSet):
    authentication_classes = []
    permission_classes = []
    serializer_class = FileVersionSerializer
    queryset = FileVersion.objects.all()
    lookup_field = "id"


class UploadFileVersionViewSet(viewsets.ModelViewSet):
    serializer_class = UploadFileVersionSerializer
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def get_queryset(self):
        # Only allow users to see their own uploads
        return FileVersion.objects.filter(uploaded_by=self.request.user)

    def perform_create(self, serializer):
        serializer.save()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_file_by_url(request, path):
    """
    Retrieve the latest or specific revision of file at given path.
    """
    user = request.user
    revision = request.GET.get("revision")

    full_url_path = f"/documents/{unquote(path)}"

    # Get all versions uploaded by the same user at that URL
    file_versions = FileVersion.objects.filter(
        uploaded_by=user,
        url_path=full_url_path
    ).order_by("-version_number")

    if not file_versions.exists():
        raise Http404("No file found at this path.")

    # Pick version based on ?revision=X or latest if not set
    if revision is not None:
        try:
            version = file_versions.filter(version_number=int(revision)).first()
            if not version:
                raise Http404("Specified revision not found.")
        except ValueError:
            raise Http404("Invalid revision number.")
    else:
        version = file_versions.first()

    return FileResponse(version.file, as_attachment=True, filename=version.file_name)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_file_by_content_hash(request, hash):
    """
    Retrieve a file by its content hash (CAS).
    """
    user = request.user
    try:
        file_version = FileVersion.objects.get(content_hash=hash, uploaded_by=user)
    except FileVersion.DoesNotExist:
        raise Http404("File not found for given hash.")

    return FileResponse(file_version.file, as_attachment=True, filename=file_version.file_name)
