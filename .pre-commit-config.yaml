exclude: '^docs/|/migrations/'
default_stages: [commit]

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-json
      - id: check-toml
      - id: check-xml
      - id: check-yaml
      - id: debug-statements
      - id: check-builtin-literals
      - id: check-case-conflict
      - id: check-docstring-first
      - id: detect-private-key

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.0-alpha.9-for-vscode
    hooks:
      - id: prettier
        args: ['--tab-width', '2', '--single-quote']
        exclude: 'propylon_document_manager/templates/'

  - repo: https://github.com/adamchainz/django-upgrade
    rev: '1.13.0'
    hooks:
      - id: django-upgrade
        args: ['--target-version', '4.1']

  - repo: https://github.com/asottile/pyupgrade
    rev: v3.6.0
    hooks:
      - id: pyupgrade
        args: [--py311-plus]

  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black

  - repo: https://github.com/PyCQA/isort
    rev: 5.12.0
    hooks:
      - id: isort

  - repo: https://github.com/PyCQA/flake8
    rev: 6.0.0
    hooks:
      - id: flake8

# sets up .pre-commit-ci.yaml to ensure pre-commit dependencies stay up to date
ci:
  autoupdate_schedule: weekly
  skip: []
  submodules: false
