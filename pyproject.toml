# ==== pytest ====
[tool.pytest.ini_options]
minversion = "6.0"
addopts = "--ds=tests.settings --reuse-db"
pythonpath = [
    ".",
    "src"
]
python_files = [
    "tests.py",
    "test_*.py",
]

# ==== Coverage ====
[tool.coverage.run]
include = ["propylon_document_manager/**"]
omit = ["*/migrations/*", "*/tests/*"]
plugins = ["django_coverage_plugin"]


# ==== black ====
[tool.black]
line-length = 119
target-version = ['py311']


# ==== isort ====
[tool.isort]
profile = "black"
line_length = 119
known_first_party = [
    "propylon_document_manager",
    "config",
]
skip = ["venv/"]
skip_glob = ["**/migrations/*.py"]


# ==== mypy ====
[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
ignore_missing_imports = true
warn_unused_ignores = true
warn_redundant_casts = true
warn_unused_configs = true
plugins = [
    "mypy_django_plugin.main",
    "mypy_drf_plugin.main",
]

[[tool.mypy.overrides]]
# Django migrations should not produce any errors:
module = "*.migrations.*"
ignore_errors = true

[tool.django-stubs]
django_settings_module = "config.settings.test"


# ==== PyLint ====
[tool.pylint.MASTER]
load-plugins = [
    "pylint_django",
]
django-settings-module = "config.settings.local"

[tool.pylint.FORMAT]
max-line-length = 119

[tool.pylint."MESSAGES CONTROL"]
disable = [
    "missing-docstring",
    "invalid-name",
]

[tool.pylint.DESIGN]
max-parents = 13

[tool.pylint.TYPECHECK]
generated-members = [
    "REQUEST",
    "acl_users",
    "aq_parent",
    "[a-zA-Z]+_set{1,2}",
    "save",
    "delete",
]
