# Generated by Django 5.0.1 on 2025-06-27 20:01

import django.core.files.storage
import django.db.models.deletion
import propylon_document_manager.file_versions.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("file_versions", "0001_initial"),
    ]

    operations = [
        migrations.AlterModelManagers(
            name="user",
            managers=[
                ("objects", propylon_document_manager.file_versions.models.UserManager()),
            ],
        ),
        migrations.AddField(
            model_name="fileversion",
            name="file",
            field=models.FileField(
                null=True,
                storage=django.core.files.storage.FileSystemStorage(location="uploaded_files/"),
                upload_to="uploads/",
            ),
        ),
        migrations.AddField(
            model_name="fileversion",
            name="uploaded_at",
            field=models.DateTimeField(auto_now_add=True, null=True),
        ),
        migrations.AddField(
            model_name="fileversion",
            name="uploaded_by",
            field=models.ForeignKey(
                blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="fileversion",
            name="url_path",
            field=models.CharField(max_length=512, null=True),
        ),
        migrations.AlterUniqueTogether(
            name="fileversion",
            unique_together={("url_path", "version_number")},
        ),
    ]
