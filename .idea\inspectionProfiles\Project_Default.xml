<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="10">
            <item index="0" class="java.lang.String" itemvalue="absl_py" />
            <item index="1" class="java.lang.String" itemvalue="easydict" />
            <item index="2" class="java.lang.String" itemvalue="boto3" />
            <item index="3" class="java.lang.String" itemvalue="tensorflow" />
            <item index="4" class="java.lang.String" itemvalue="tensorflow_gpu" />
            <item index="5" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="6" class="java.lang.String" itemvalue="absl" />
            <item index="7" class="java.lang.String" itemvalue="numpy" />
            <item index="8" class="java.lang.String" itemvalue="Flask" />
            <item index="9" class="java.lang.String" itemvalue="Pillow" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>