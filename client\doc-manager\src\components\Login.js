import React, { useState } from "react";

function Login({ setLoggedIn }) {
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");

    const handleLogin = async (e) => {
        e.preventDefault();

        const res = await fetch("http://localhost:8000/auth-token/", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ username: email, password }),
        });

        if (res.ok) {
            const data = await res.json();
            localStorage.setItem("token", data.token);
            setLoggedIn(true);
            window.location.href = "/";
        } else {
            alert("Login failed");
        }
    };

    return (
        <div style={{display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',}}>
            <form onSubmit={handleLogin}>
                <h2 style={{ textAlign: "center" }}><PERSON>gin</h2>
                <div style={{ display: 'flex', flexDirection: 'column', flex: 1 }}>
                    <label className="version_label">Email</label>
                    <input
                        type="email"
                        placeholder="Email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                    />
                </div>
                <div style={{ display: 'flex', flexDirection: 'column', flex: 1 }}>
                    <label className="version_label">Password</label>
                    <input
                        type="password"
                        placeholder="Password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                    />
                </div>
                <button type="submit">Login</button>
                <p>
                    Don't have an account? <a href="/register">Register here</a>
                </p>
            </form>
        </div>
        
    );
}

export default Login;