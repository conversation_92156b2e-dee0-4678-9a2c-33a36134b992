import React, { useState } from "react";

function Register() {
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");

    const handleRegister = async (e) => {
        e.preventDefault();

        const res = await fetch("http://localhost:8000/api/register/", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ email, password }),
        });

        if (res.ok) {
            alert("Registered! Now log in.");
            window.location.href = "/login";
        } else {
            alert("Registration failed");
        }
    };

    return (
        <div style={{display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',}}>
            <form onSubmit={handleRegister}>
                <h2 style={{ textAlign: "center" }}>Register</h2>
                <div style={{ display: 'flex', flexDirection: 'column', flex: 1 }}>
                    <label className="version_label">Email</label>
                    <input
                        type="email"
                        placeholder="Email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                    />
                </div>
                <div style={{ display: 'flex', flexDirection: 'column', flex: 1 }}>
                    <label className="version_label">Password</label>
                    <input
                        type="password"
                        placeholder="Password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                    />
                </div>
                <button type="submit">Register</button>
                <p>
                    Already have an account? <a href="/login">login here</a>
                </p>
            </form>
        </div>
        
    );
}

export default Register;