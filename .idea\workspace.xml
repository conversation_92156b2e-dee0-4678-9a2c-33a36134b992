<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="b696045a-ea49-426c-8c33-449ec8ac473e" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.gitignore" beforeDir="false" afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/doc-manager/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/client/doc-manager/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/doc-manager/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/client/doc-manager/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/doc-manager/src/App.js" beforeDir="false" afterPath="$PROJECT_DIR$/client/doc-manager/src/App.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/doc-manager/src/FileVersions.js" beforeDir="false" afterPath="$PROJECT_DIR$/client/doc-manager/src/FileVersions.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/manage.py" beforeDir="false" afterPath="$PROJECT_DIR$/manage.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/propylon_document_manager/file_versions/api/serializers.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/propylon_document_manager/file_versions/api/serializers.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/propylon_document_manager/file_versions/api/views.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/propylon_document_manager/file_versions/api/views.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/propylon_document_manager/file_versions/apps.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/propylon_document_manager/file_versions/apps.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/propylon_document_manager/file_versions/models.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/propylon_document_manager/file_versions/models.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/propylon_document_manager/site/api_router.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/propylon_document_manager/site/api_router.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/propylon_document_manager/site/settings/base.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/propylon_document_manager/site/settings/base.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/propylon_document_manager/site/urls.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/propylon_document_manager/site/urls.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tests/test_file_versions.py" beforeDir="false" afterPath="$PROJECT_DIR$/tests/test_file_versions.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="2yRN6RyMdVqx4IV5AnFxrBggo66" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="settings.editor.selected.configurable" value="preferences.pluginManager" />
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="b696045a-ea49-426c-8c33-449ec8ac473e" name="Changes" comment="" />
      <created>1749793251494</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749793251494</updated>
    </task>
    <servers />
  </component>
</project>